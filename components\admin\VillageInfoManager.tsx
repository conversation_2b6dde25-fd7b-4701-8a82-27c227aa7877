'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { supabase } from '@/lib/supabase';
import { Save, RefreshCw } from 'lucide-react';

interface VillageInfo {
  id: string;
  name: string;
  history: string;
  vision: string;
  mission: string;
  geography: string;
  demographics: any;
}

export function VillageInfoManager() {
  const [villageInfo, setVillageInfo] = useState<VillageInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    history: '',
    vision: '',
    mission: '',
    geography: '',
    population: 0,
    families: 0,
    males: 0,
    females: 0,
  });

  useEffect(() => {
    fetchVillageInfo();
  }, []);

  const fetchVillageInfo = async () => {
    try {
      const { data } = await supabase
        .from('village_info')
        .select('*')
        .single();

      if (data) {
        setVillageInfo(data);
        setFormData({
          name: data.name,
          history: data.history,
          vision: data.vision,
          mission: data.mission,
          geography: data.geography,
          population: data.demographics?.population || 0,
          families: data.demographics?.families || 0,
          males: data.demographics?.males || 0,
          females: data.demographics?.females || 0,
        });
      }
    } catch (error) {
      console.error('Error fetching village info:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const demographics = {
        population: formData.population,
        families: formData.families,
        males: formData.males,
        females: formData.females,
      };

      if (villageInfo) {
        // Update existing
        const { error } = await supabase
          .from('village_info')
          .update({
            name: formData.name,
            history: formData.history,
            vision: formData.vision,
            mission: formData.mission,
            geography: formData.geography,
            demographics,
            updated_at: new Date().toISOString(),
          })
          .eq('id', villageInfo.id);

        if (error) throw error;
      } else {
        // Create new
        const { error } = await supabase
          .from('village_info')
          .insert([{
            name: formData.name,
            history: formData.history,
            vision: formData.vision,
            mission: formData.mission,
            geography: formData.geography,
            demographics,
          }]);

        if (error) throw error;
      }

      await fetchVillageInfo();
      alert('Data profil desa berhasil disimpan!');
    } catch (error) {
      console.error('Error saving village info:', error);
      alert('Gagal menyimpan data profil desa');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Kelola Profil Desa</h2>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={fetchVillageInfo}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            <Save className="w-4 h-4 mr-2" />
            {saving ? 'Menyimpan...' : 'Simpan'}
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Informasi Dasar</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="name">Nama Desa</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Masukkan nama desa"
              />
            </div>
            <div>
              <Label htmlFor="history">Sejarah</Label>
              <Textarea
                id="history"
                value={formData.history}
                onChange={(e) => setFormData({ ...formData, history: e.target.value })}
                placeholder="Tulis sejarah desa..."
                rows={6}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Visi & Misi</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="vision">Visi</Label>
              <Textarea
                id="vision"
                value={formData.vision}
                onChange={(e) => setFormData({ ...formData, vision: e.target.value })}
                placeholder="Tulis visi desa..."
                rows={4}
              />
            </div>
            <div>
              <Label htmlFor="mission">Misi</Label>
              <Textarea
                id="mission"
                value={formData.mission}
                onChange={(e) => setFormData({ ...formData, mission: e.target.value })}
                placeholder="Tulis misi desa..."
                rows={4}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Geografi</CardTitle>
          </CardHeader>
          <CardContent>
            <div>
              <Label htmlFor="geography">Kondisi Geografis</Label>
              <Textarea
                id="geography"
                value={formData.geography}
                onChange={(e) => setFormData({ ...formData, geography: e.target.value })}
                placeholder="Deskripsikan kondisi geografis desa..."
                rows={4}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Data Demografi</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="population">Total Penduduk</Label>
                <Input
                  id="population"
                  type="number"
                  value={formData.population}
                  onChange={(e) => setFormData({ ...formData, population: parseInt(e.target.value) || 0 })}
                />
              </div>
              <div>
                <Label htmlFor="families">Kepala Keluarga</Label>
                <Input
                  id="families"
                  type="number"
                  value={formData.families}
                  onChange={(e) => setFormData({ ...formData, families: parseInt(e.target.value) || 0 })}
                />
              </div>
              <div>
                <Label htmlFor="males">Laki-laki</Label>
                <Input
                  id="males"
                  type="number"
                  value={formData.males}
                  onChange={(e) => setFormData({ ...formData, males: parseInt(e.target.value) || 0 })}
                />
              </div>
              <div>
                <Label htmlFor="females">Perempuan</Label>
                <Input
                  id="females"
                  type="number"
                  value={formData.females}
                  onChange={(e) => setFormData({ ...formData, females: parseInt(e.target.value) || 0 })}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}