/*
  # Village Profile Database Schema

  1. New Tables
    - `village_info` - Store village profile information (history, vision, mission, etc.)
    - `announcements` - Store village announcements and news
    - `umkm` - Store UMKM (local business) directory
    - `contacts` - Store village officials contact information

  2. Security
    - Enable RLS on all tables
    - Add policies for public read access
    - Add policies for authenticated admin users to manage content
*/

-- Create village_info table
CREATE TABLE IF NOT EXISTS village_info (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL DEFAULT '',
  history text NOT NULL DEFAULT '',
  vision text NOT NULL DEFAULT '',
  mission text NOT NULL DEFAULT '',
  geography text NOT NULL DEFAULT '',
  demographics jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create announcements table
CREATE TABLE IF NOT EXISTS announcements (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  content text NOT NULL,
  is_important boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create umkm table
CREATE TABLE IF NOT EXISTS umkm (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text NOT NULL,
  category text NOT NULL DEFAULT 'Lainnya',
  owner_name text NOT NULL,
  contact_phone text NOT NULL,
  contact_email text,
  address text NOT NULL,
  image_url text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create contacts table
CREATE TABLE IF NOT EXISTS contacts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  position text NOT NULL,
  phone text NOT NULL,
  email text,
  office_hours text NOT NULL DEFAULT '08:00 - 16:00',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE village_info ENABLE ROW LEVEL SECURITY;
ALTER TABLE announcements ENABLE ROW LEVEL SECURITY;
ALTER TABLE umkm ENABLE ROW LEVEL SECURITY;
ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access
CREATE POLICY "Allow public read access on village_info"
  ON village_info FOR SELECT
  TO anon, authenticated
  USING (true);

CREATE POLICY "Allow public read access on announcements"
  ON announcements FOR SELECT
  TO anon, authenticated
  USING (true);

CREATE POLICY "Allow public read access on umkm"
  ON umkm FOR SELECT
  TO anon, authenticated
  USING (true);

CREATE POLICY "Allow public read access on contacts"
  ON contacts FOR SELECT
  TO anon, authenticated
  USING (true);

-- Create policies for authenticated admin users (full access)
CREATE POLICY "Allow authenticated users to manage village_info"
  ON village_info FOR ALL
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated users to manage announcements"
  ON announcements FOR ALL
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated users to manage umkm"
  ON umkm FOR ALL
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated users to manage contacts"
  ON contacts FOR ALL
  TO authenticated
  USING (true);

-- Insert default village info
INSERT INTO village_info (name, history, vision, mission, geography, demographics) VALUES (
  'Desa Contoh',
  'Sejarah desa ini dimulai pada tahun 1850...',
  'Menjadi desa yang maju, mandiri, dan sejahtera',
  'Meningkatkan kesejahteraan masyarakat melalui pembangunan yang berkelanjutan',
  'Terletak di dataran tinggi dengan luas wilayah 25 km²',
  '{"population": 2500, "families": 650, "males": 1250, "females": 1250}'
) ON CONFLICT DO NOTHING;

-- Insert sample announcements
INSERT INTO announcements (title, content, is_important) VALUES 
('Rapat Warga Bulanan', 'Rapat warga akan dilaksanakan pada tanggal 15 setiap bulannya di balai desa.', false),
('Perbaikan Jalan Utama', 'Pembangunan jalan utama akan dimulai minggu depan. Mohon kerjasamanya.', true)
ON CONFLICT DO NOTHING;

-- Insert sample UMKM
INSERT INTO umkm (name, description, category, owner_name, contact_phone, address) VALUES 
('Warung Bu Sari', 'Warung makan dengan menu masakan rumahan yang lezat', 'Kuliner', 'Sari Wulandari', '08123456789', 'Jl. Raya Desa No. 15'),
('Kerajinan Bambu Pak Joko', 'Memproduksi berbagai kerajinan dari bambu', 'Kerajinan', 'Joko Santoso', '08567890123', 'Kampung Krajan RT 02/01')
ON CONFLICT DO NOTHING;

-- Insert sample contacts
INSERT INTO contacts (name, position, phone, office_hours) VALUES 
('Budi Prasetyo', 'Kepala Desa', '08111222333', '08:00 - 15:00'),
('Siti Aminah', 'Sekretaris Desa', '08444555666', '08:00 - 15:00')
ON CONFLICT DO NOTHING;