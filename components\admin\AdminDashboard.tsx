'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/lib/supabase';
import { LogOut, Settings, BarChart3, Database } from 'lucide-react';
import { VillageInfoManager } from './VillageInfoManager';
import { AnnouncementManager } from './AnnouncementManager';
import { UMKMManager } from './UMKMManager';
import { ContactManager } from './ContactManager';
import { StorageMonitor } from './StorageMonitor';

interface AdminDashboardProps {
  user: any;
}

type ActiveSection = 'overview' | 'village' | 'announcements' | 'umkm' | 'contacts' | 'storage';

export function AdminDashboard({ user }: AdminDashboardProps) {
  const [activeSection, setActiveSection] = useState<ActiveSection>('overview');

  const handleLogout = async () => {
    await supabase.auth.signOut();
    window.location.reload();
  };

  const menuItems = [
    { id: 'overview', label: 'Ringkasan', icon: BarChart3 },
    { id: 'village', label: 'Profil Desa', icon: Settings },
    { id: 'announcements', label: 'Pengumuman', icon: Database },
    { id: 'umkm', label: 'UMKM', icon: Database },
    { id: 'contacts', label: 'Kontak', icon: Database },
    { id: 'storage', label: 'Storage', icon: Database },
  ];

  const renderContent = () => {
    switch (activeSection) {
      case 'village':
        return <VillageInfoManager />;
      case 'announcements':
        return <AnnouncementManager />;
      case 'umkm':
        return <UMKMManager />;
      case 'contacts':
        return <ContactManager />;
      case 'storage':
        return <StorageMonitor />;
      default:
        return (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total UMKM</p>
                    <p className="text-2xl font-bold">-</p>
                  </div>
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Database className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pengumuman Aktif</p>
                    <p className="text-2xl font-bold">-</p>
                  </div>
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <Database className="w-6 h-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Kontak Terdaftar</p>
                    <p className="text-2xl font-bold">-</p>
                  </div>
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Database className="w-6 h-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Storage Used</p>
                    <p className="text-2xl font-bold">-</p>
                  </div>
                  <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <Database className="w-6 h-6 text-orange-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-lg">
        <div className="p-6 border-b">
          <h2 className="text-xl font-bold text-gray-800">Admin Panel</h2>
          <p className="text-sm text-gray-600">{user.email}</p>
        </div>
        
        <nav className="p-4">
          <ul className="space-y-2">
            {menuItems.map((item) => {
              const Icon = item.icon;
              return (
                <li key={item.id}>
                  <button
                    onClick={() => setActiveSection(item.id as ActiveSection)}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                      activeSection === item.id
                        ? 'bg-green-100 text-green-700'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{item.label}</span>
                  </button>
                </li>
              );
            })}
          </ul>
        </nav>
        
        <div className="absolute bottom-4 left-4">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleLogout}
            className="flex items-center space-x-2"
          >
            <LogOut className="w-4 h-4" />
            <span>Logout</span>
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">
              {menuItems.find(item => item.id === activeSection)?.label || 'Dashboard'}
            </h1>
          </div>
          
          {renderContent()}
        </div>
      </div>
    </div>
  );
}